"use client";

import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { ThemeToggle } from "@/components/theme-toggle";
import { LogOut, Menu, User } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useAuth } from "@/hooks/use-auth";
import { UserAvatar } from "@/components/user-avatar";

// Define a flexible user type to handle different user data structures
type FlexibleUser = {
  name?: string;
  fullName?: string;
  email?: string;
  company?: string;
  department?: string;
  jobRole?: string;
  profileImage?: string;
  [key: string]: unknown;
};

const navItems = [
  { name: "Agents", href: "/marketplace/agents" },
  { name: "MCPs", href: "/marketplace/mcps" },
  { name: "Workflows", href: "/marketplace/workflows" },
];

export function Header() {
  const pathname = usePathname();

  // Use our auth hook
  const { user: originalUser, isAuthenticated, isLoading, logout, handleLoginRedirect } = useAuth();

  // Cast user to FlexibleUser type to handle different user data structures
  const user = originalUser as FlexibleUser;

  return (
    <header className="sticky top-0 z-50 w-full border-b border-gray-200 bg-white/80 backdrop-blur-sm dark:border-gray-800 dark:bg-gray-950/80">
      <div className="container flex h-16 items-center justify-between">
        <div className="flex items-center gap-4 md:gap-8">
          <Link href="/" className="flex items-center space-x-2">
            <Image
              src="/logo.svg"
              alt="RUH Logo"
              width={32}
              height={32}
              className="h-8 w-16"
            />
            {
              /* 
                          <div className="flex flex-col">
              <span className="text-lg font-bold leading-none text-foreground">RUH</span>
              <span className="text-xs text-muted-foreground">marketplace</span>
            </div>
              */
            }
          </Link>
          <nav className="hidden md:flex gap-6">
            {navItems.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  "text-sm font-medium transition-colors hover:text-foreground",
                  pathname === item.href
                    ? "text-foreground font-semibold"
                    : "text-muted-foreground"
                )}
              >
                {item.name}
              </Link>
            ))}
          </nav>
        </div>
        <div className="flex items-center gap-3">
          <ThemeToggle />

          {isLoading ? (
            // Show loading state
            <Button variant="ghost" size="icon" className="rounded-full">
              <div className="h-8 w-8 rounded-full bg-gray-200 dark:bg-gray-700 animate-pulse"></div>
            </Button>
          ) : isAuthenticated && user ? (
            // Show user avatar and menu when authenticated
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="rounded-full">
                  <UserAvatar user={user} className="h-8 w-8" fallback="U" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium leading-none">{user.fullName || user.name || 'User'}</p>
                    <p className="text-xs leading-none text-gray-500">{user.email || ''}</p>
                    {/* {user.jobRole && user.company && (
                      <p className="text-xs leading-none text-gray-500 mt-1">
                        {user.jobRole} at {user.company}
                      </p>
                    )} */}
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/profile" className="cursor-pointer">
                    <User className="mr-2 h-4 w-4" />
                    <span>Profile</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => logout()} className="cursor-pointer">
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Log out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            // Show login button when not authenticated
            <Button
              variant="default"
              size="sm"
              onClick={() => handleLoginRedirect()}
            >
              Log In
            </Button>
          )}

          {/* Mobile Menu */}
          <div className="md:hidden">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <Menu className="h-5 w-5" />
                  <span className="sr-only">Toggle menu</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {navItems.map((item) => (
                  <DropdownMenuItem key={item.href} asChild>
                    <Link href={item.href}>{item.name}</Link>
                  </DropdownMenuItem>
                ))}
                {isLoading ? (
                  <DropdownMenuItem disabled>
                    <div className="h-4 w-20 bg-gray-200 dark:bg-gray-700 animate-pulse rounded"></div>
                  </DropdownMenuItem>
                ) : isAuthenticated ? (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem asChild>
                      <Link href="/profile">Profile</Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => logout()}>
                      Log Out
                    </DropdownMenuItem>
                  </>
                ) : (
                  <DropdownMenuItem onClick={() => handleLoginRedirect()}>
                    Log In
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </header>
  );
}
