"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { LogIn } from "lucide-react";
import { getAuthUrl } from "@/lib/helpers";

interface LoginDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export function LoginDialog({ isOpen, onOpenChange }: LoginDialogProps) {
  const handleLogin = () => {
    // Get the auth URL
    const authUrl = getAuthUrl();

    // Add current path as redirect parameter
    const redirectUrl = `${authUrl}/?redirect_url=${encodeURIComponent(window.location.href)}`;

    // Redirect to login page
    window.location.href = redirectUrl;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Authentication Required</DialogTitle>
          <DialogDescription>
            You need to be logged in to connect to this MCP server.
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          <p className="text-sm text-muted-foreground mb-4">
            Please log in to your RUH account to continue. After logging in, you&apos;ll be redirected back to this page.
          </p>
        </div>
        <DialogFooter>
          <Button onClick={handleLogin} className="w-full">
            <LogIn className="mr-2 h-4 w-4" />
            Log In
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
