"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Sheet,
  She<PERSON><PERSON>ontent,
  She<PERSON><PERSON>eader,
  SheetTitle,
  SheetDescription,
  SheetFooter,
  SheetClose, // Added for a close button if needed, or rely on overlay click/escape
} from "@/components/ui/sheet";
import { Badge } from "@/components/ui/badge";
import { Settings, Zap, AlertTriangle } from "lucide-react"; // Added AlertTriangle for banner

// Matches the one in MCPDetailPage, consider moving to a shared types file if used elsewhere
export interface EnvConfigKey {
  key: string;
  description: string;
}

interface McpConnectionSheetProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  envConfigKeys: EnvConfigKey[];
  onConnect: (envValues: Record<string, string>) => void;
  isConnected: boolean;
  isLoading?: boolean;
  mcpTitle?: string; // To display the MCP name in the sheet
  userEnvValues?: Record<string, string>; // User's existing environment values
}

export function McpConnectionSheet({
  isOpen,
  onOpenChange,
  envConfigKeys = [],
  onConnect,
  isConnected,
  isLoading = false,
  mcpTitle = "MCP Server",
  userEnvValues = {},
}: McpConnectionSheetProps) {
  const [localEnvValues, setLocalEnvValues] = useState<Record<string, string>>({});

  // Initialize local env values with user's existing values when sheet opens
  useEffect(() => {
    if (isOpen && userEnvValues) {
      setLocalEnvValues(userEnvValues);
    }
  }, [isOpen, userEnvValues]);

  const handleLocalEnvValueChange = (key: string, value: string) => {
    setLocalEnvValues((prev) => ({ ...prev, [key]: value }));
  };

  const handleSubmit = () => {
    onConnect(localEnvValues);
  };

  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetContent className="sm:max-w-lg"> {/* Adjust width as needed */}
        <SheetHeader className="mb-6">
          <SheetTitle className="text-2xl">MCP Server Connection</SheetTitle>
          <SheetDescription>
            Connect to your {mcpTitle} to test and run tools.
          </SheetDescription>
        </SheetHeader>

        <div className="space-y-4 mb-6">
          <div className="flex items-center justify-between p-3 bg-muted/50 rounded-md">
            <div className="flex items-center gap-2">
              <Settings className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Connection Status</span>
            </div>
            <Badge variant={isConnected ? "default" : "outline"} className={isConnected ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300" : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"}>
              {isConnected ? "Connected" : "Not Connected"}
              {/* Consider adding "Offline" if detectable, or "Connecting..." if isLoading */}
            </Badge>
          </div>

          {envConfigKeys.length > 0 ? (
            envConfigKeys.map((envKey) => (
              <div key={envKey.key} className="space-y-1.5">
                <label htmlFor={envKey.key} className="block text-sm font-medium">
                  {envKey.key}
                </label>
                <Input
                  id={envKey.key}
                  type="password"
                  value={localEnvValues[envKey.key] || ""}
                  onChange={(e) => handleLocalEnvValueChange(envKey.key, e.target.value)}
                  placeholder={envKey.description || `Enter ${envKey.key}`}
                  disabled={isConnected || isLoading}
                  className="w-full"
                />
                {envKey.description && (
                    <p className="text-xs text-muted-foreground pt-1">{envKey.description}</p>
                )}
              </div>
            ))
          ) : (
            <p className="text-sm text-muted-foreground text-center py-4">
              No specific server key or API token required for this MCP.
            </p>
          )}
        </div>

        <div className="mb-6 p-4 border rounded-md bg-background">
            <h4 className="font-medium mb-2 text-md">Quick Setup Guide</h4>
            <ul className="list-decimal list-inside space-y-1 text-sm text-muted-foreground">
                <li>Start your MCP server locally or deploy it.</li>
                <li>Get your server key or API token (if required).</li>
                <li>Enter the key above and click Connect.</li>
                <li>Test tools in the Tools tab.</li>
            </ul>
        </div>


        <SheetFooter>
          <Button
            onClick={handleSubmit}
            disabled={isConnected || isLoading || (envConfigKeys.length > 0 && !envConfigKeys.every(k => !!localEnvValues[k.key]))}
            className="w-full"
            size="lg"
          >
            <Zap className="mr-2 h-4 w-4" />
            {isLoading ? "Connecting..." : isConnected ? "Connected" : "Connect"}
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}