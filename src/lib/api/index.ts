import axiosClient from '@/lib/axios-client';
import { cookieUtils } from '@/lib/cookie';
import { userService } from '@/services/user-service';
import { agentService } from './services/agent.service';
import { combinedMarketplaceService } from './services/combined-marketplace.service';
import { marketplaceService } from './services/marketplace.service';
import { mcpService } from './services/mcp.service';
import { workflowService } from './services/workflow.service';

// Import mock services

// Export all services
export const api = {
  user: userService,
  marketplace: marketplaceService,
  // mcp: useMockServices ? mockMCPService : mcpService,
  mcp: mcpService,
  // agent: useMockServices ? mockAgentService : agentService,
  agent: agentService,
  // workflow: useMockServices ? mockWorkflowService : workflowService,
  workflow: workflowService,
  // combinedMarketplace: useMockServices ? mockCombinedMarketplaceService : combinedMarketplaceService,
  combinedMarketplace: combinedMarketplaceService,
};

// Export the axios instance and cookie utilities
export { axiosClient, cookieUtils };

// Export types
  export * from './types';

