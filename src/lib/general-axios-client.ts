import axios from 'axios';
import { cookieUtils } from './cookie';

// Define base API URL from environment variable or use a default
const BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'https://e91e-106-215-176-118.ngrok-free.app/api/v1';

// Log the API URL for debugging
console.log('General API Base URL:', BASE_URL);
console.log('Environment variable:', process.env.NEXT_PUBLIC_API_URL);

// Create an Axios instance for general API endpoints (non-marketplace)
const generalAxiosClient = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 50000, // 50 seconds
});

// Request interceptor for adding auth token
generalAxiosClient.interceptors.request.use(
  (config) => {
    // Only run on client side
    if (typeof window !== 'undefined') {
      const accessToken = cookieUtils.getAccessToken();
      const tokenType = 'Bearer';

      if (accessToken && config.headers) {
        config.headers.Authorization = `${tokenType} ${accessToken}`;
      }
    }
    config.headers["ngrok-skip-browser-warning"] = "true";
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling common errors
generalAxiosClient.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('General API Error:', error);
    // Handle common errors (e.g., 401 unauthorized, etc.)
    if (error.response?.status === 401) {
      // Handle unauthorized error (e.g., redirect to login)
      console.error('Unauthorized access');
      // Optional: redirect to login page
      // window.location.href = '/auth/login';
    }
    return Promise.reject(error);
  }
);

export default generalAxiosClient;
