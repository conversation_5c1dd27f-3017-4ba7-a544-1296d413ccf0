export const getAuthUrl = () => process.env.NEXT_PUBLIC_AUTH_URL || "https://ruh-auth.rapidinnovation.dev/login";

/**
 * Returns the URL for the RUH Developer Platform
 * @returns {string} The URL for the RUH Developer Platform
 */
export const getDeveloperPlatformUrl = () => process.env.NEXT_PUBLIC_DEVELOPER_PLATFORM_URL || "https://ruh-developer.rapidinnovation.dev/";

/**
 * Redirects the user to the RUH Developer Platform
 * Opens in a new tab by default
 */
export const redirectToDeveloperPlatform = () => {
  window.open(getDeveloperPlatformUrl(), "_blank");
};

export const getRuhUrl = () => process.env.NEXT_PUBLIC_RUH_URL || "";